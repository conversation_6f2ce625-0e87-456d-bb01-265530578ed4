import React, { useState, useEffect } from 'react';

export const ImageRequirementsCarousel = ({ isOpen, onClose }) => {
    const [currentSlide, setCurrentSlide] = useState(0);
    const [isClosing, setIsClosing] = useState(false);
    const [slideTransition, setSlideTransition] = useState(false);

    // Carousel data with four slides
    const slides = [
        {
            id: 'face-direction',
            title: 'Face Direction Matters',
            description: 'For optimal results, use photos where the face is clearly directed toward the camera.',
            leftImage: {
                src: '/assets/persona/Wrong-direction-face.webp',
                alt: 'Wrong direction - face turned away',
                label: '❌ Avoid: Face turned away'
            },
            rightImage: {
                src: '/assets/persona/Correct-direction-face01.webp',
                alt: 'Correct direction - face toward camera',
                label: '✅ Best: Face toward camera'
            }
        },
        {
            id: 'clear-closeup',
            title: 'Clear Close-up Shot',
            description: 'Use well-lit, high-quality close-up photos that clearly show facial features with good detail and resolution.',
            leftImage: {
                src: '/assets/persona/Correct-image01.webp',
                alt: 'Face turned away from camera',
                label: '❌ Avoid: Face turned away'
            },
            rightImage: {
                src: '/assets/persona/Correct-image02.webp',
                alt: 'High-quality portrait with clear features',
                label: '✅ Perfect: Clear facial details'
            }
        },
        {
            id: 'crowd-clarity',
            title: 'Avoid Crowded Photos',
            description: 'Choose images with a single, clearly visible person. Crowded photos reduce accuracy.',
            leftImage: {
                src: '/assets/persona/Crowded-pic01.webp',
                alt: 'Crowded photo with multiple people',
                label: '❌ Avoid: Multiple people'
            },
            rightImage: {
                src: '/assets/persona/Crowded-pic02.webp',
                alt: 'Crowded photo with multiple people',
                label: '❌ Avoid: Multiple people'
            }
        },
        {
            id: 'glasses-clarity',
            title: 'Clear Facial Features',
            description: 'Ensure facial features are clearly visible without heavy obstruction from accessories.',
            leftImage: {
                src: '/assets/persona/Wrong-image-glasses.webp',
                alt: 'Face obscured by dark glasses',
                label: '❌ Avoid: Heavy obstruction'
            },
            rightImage: {
                src: '/assets/persona/correct-image-glasses.webp',
                alt: 'Clear face with light glasses',
                label: '✅ Best: Clear features'
            }
        }
    ];

    // Handle slide navigation
    const goToSlide = (slideIndex) => {
        if (slideIndex === currentSlide) return;
        
        setSlideTransition(true);
        setTimeout(() => {
            setCurrentSlide(slideIndex);
            setSlideTransition(false);
        }, 150);
    };

    const nextSlide = () => {
        const nextIndex = (currentSlide + 1) % slides.length;
        goToSlide(nextIndex);
    };

    const prevSlide = () => {
        const prevIndex = (currentSlide - 1 + slides.length) % slides.length;
        goToSlide(prevIndex);
    };

    // Handle modal close with animation
    const handleClose = () => {
        setIsClosing(true);
        setTimeout(() => {
            setIsClosing(false);
            onClose();
        }, 300);
    };

    // Keyboard navigation
    useEffect(() => {
        if (!isOpen) return;

        const handleKeyDown = (event) => {
            switch (event.key) {
                case 'Escape':
                    handleClose();
                    break;
                case 'ArrowLeft':
                    prevSlide();
                    break;
                case 'ArrowRight':
                    nextSlide();
                    break;
                case '1':
                case '2':
                case '3':
                case '4':
                    const slideIndex = parseInt(event.key) - 1;
                    if (slideIndex >= 0 && slideIndex < slides.length) {
                        goToSlide(slideIndex);
                    }
                    break;
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [isOpen, currentSlide]);

    // Auto-hide scrollbar during transition
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = '';
        }

        return () => {
            document.body.style.overflow = '';
        };
    }, [isOpen]);

    if (!isOpen) return null;

    const currentSlideData = slides[currentSlide];

    return React.createElement('div', {
        className: `fixed inset-0 z-[10003] flex items-center justify-center p-4 transition-all duration-300 ease-out ${
            isClosing ? 'opacity-0 scale-95' : 'opacity-100 scale-100'
        }`,
        style: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            backdropFilter: 'blur(8px)',
            WebkitBackdropFilter: 'blur(8px)'
        },
        onClick: handleClose,
        'aria-modal': true,
        'aria-labelledby': 'carousel-title',
        role: 'dialog'
    },
        // Modal Content Container - MacOS Liquid Glass Design with 85% viewport height
        React.createElement('div', {
            className: `image-requirements-carousel-modal relative w-full max-w-6xl mx-auto transition-all duration-300 ease-out flex flex-col ${
                isClosing ? 'opacity-0 scale-95 translate-y-4' : 'opacity-100 scale-100 translate-y-0'
            }`,
            onClick: (e) => e.stopPropagation(),
            style: {
                background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
                backdropFilter: 'blur(20px)',
                WebkitBackdropFilter: 'blur(20px)',
                border: '1px solid rgba(255,255,255,0.1)',
                borderRadius: '24px',
                boxShadow: '0 32px 64px rgba(0,0,0,0.4), 0 16px 32px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.1)',
                minHeight: '85vh',
                maxHeight: '90vh'
            }
        },
            // Header Section - Fixed at top
            React.createElement('div', {
                className: 'flex items-center justify-between p-6 border-b border-white/10 flex-shrink-0'
            },
                React.createElement('div', {
                    className: 'flex items-center gap-3'
                },
                    React.createElement('span', {
                        className: 'iconify text-blue-400',
                        'data-icon': 'solar:image-bold-duotone',
                        style: { fontSize: '24px' }
                    }),
                    React.createElement('h1', {
                        id: 'carousel-title',
                        className: 'text-2xl font-semibold text-white'
                    }, 'Image Requirements Guide')
                ),
                React.createElement('button', {
                    onClick: handleClose,
                    className: 'p-2 rounded-xl bg-white/5 hover:bg-white/10 text-gray-300 hover:text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400/50',
                    'aria-label': 'Close requirements guide'
                },
                    React.createElement('span', {
                        className: 'iconify',
                        'data-icon': 'solar:close-circle-linear',
                        style: { fontSize: '24px' }
                    })
                )
            ),
            
            // Slide Content Area - Compact layout
            React.createElement('div', {
                className: 'carousel-content-area p-6 flex-1 overflow-y-auto'
            },
                // Slide Header
                React.createElement('div', {
                    className: 'text-center mb-6'
                },
                    React.createElement('h2', {
                        className: `text-2xl font-bold text-white mb-3 transition-all duration-300 ${
                            slideTransition ? 'opacity-0 translate-y-2' : 'opacity-100 translate-y-0'
                        }`
                    }, currentSlideData.title),
                    React.createElement('p', {
                        className: `text-lg text-gray-300 max-w-2xl mx-auto transition-all duration-300 ${
                            slideTransition ? 'opacity-0 translate-y-2' : 'opacity-100 translate-y-0'
                        }`
                    }, currentSlideData.description)
                ),
                
                // Image Comparison Section - Reduced size for better fit
                React.createElement('div', {
                    className: `grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto transition-all duration-300 ${
                        slideTransition ? 'opacity-0 scale-98' : 'opacity-100 scale-100'
                    }`
                },
                    // Left Image (Dynamic styling based on content)
                    React.createElement('div', {
                        className: 'comparison-card group'
                    },
                        React.createElement('div', {
                            className: `relative overflow-hidden rounded-2xl p-5 transition-all duration-300 group-hover:scale-[1.02] group-hover:shadow-2xl ${
                                currentSlideData.leftImage.label.includes('✅') || currentSlideData.leftImage.label.includes('Perfect') || currentSlideData.leftImage.label.includes('Best')
                                    ? 'bg-gradient-to-br from-green-500/20 to-green-600/20 border border-green-500/30'
                                    : 'bg-gradient-to-br from-red-500/20 to-red-600/20 border border-red-500/30'
                            }`,
                            style: {
                                backdropFilter: 'blur(10px)',
                                WebkitBackdropFilter: 'blur(10px)'
                            }
                        },
                            React.createElement('div', {
                                className: 'rounded-xl overflow-hidden bg-gray-800/50 mb-3 relative',
                                style: { width: '337px', height: '505px' }
                            },
                                React.createElement('img', {
                                    src: currentSlideData.leftImage.src,
                                    alt: currentSlideData.leftImage.alt,
                                    className: 'w-full h-full object-cover transition-transform duration-300 group-hover:scale-105',
                                    loading: 'lazy'
                                }),
                                React.createElement('div', {
                                    className: `absolute inset-0 transition-colors duration-300 ${
                                        currentSlideData.leftImage.label.includes('✅') || currentSlideData.leftImage.label.includes('Perfect') || currentSlideData.leftImage.label.includes('Best')
                                            ? 'bg-green-500/10 group-hover:bg-green-500/5'
                                            : 'bg-red-500/10 group-hover:bg-red-500/5'
                                    }`
                                }),
                                // Contextual Status Icon Overlay
                                React.createElement('div', {
                                    className: 'absolute top-3 right-3 z-10',
                                    style: {
                                     backdropFilter: 'blur(0)',
                                        WebkitBackdropFilter: 'blur(0)'
                                    }
                                },
                                    React.createElement('div', {
                                        className: `status-icon-overlay rounded-full p-1.5 ${
                                            currentSlideData.leftImage.label.includes('✅') || currentSlideData.leftImage.label.includes('Perfect') || currentSlideData.leftImage.label.includes('Best')
                                                ? 'success'
                                                : 'error'
                                        }`,
                                        'aria-label': currentSlideData.leftImage.label.includes('✅') || currentSlideData.leftImage.label.includes('Perfect') || currentSlideData.leftImage.label.includes('Best')
                                            ? 'Correct example'
                                            : 'Incorrect example'
                                    },
                                        React.createElement('span', {
                                            className: `iconify transition-all duration-300 ${
                                                currentSlideData.leftImage.label.includes('✅') || currentSlideData.leftImage.label.includes('Perfect') || currentSlideData.leftImage.label.includes('Best')
                                                    ? 'text-green-600'
                                                    : 'text-red-600'
                                            }`,
                                            'data-icon': currentSlideData.leftImage.label.includes('✅') || currentSlideData.leftImage.label.includes('Perfect') || currentSlideData.leftImage.label.includes('Best')
                                                ? 'solar:check-circle-bold-duotone'
                                                : 'solar:close-square-bold-duotone',
                                            style: { fontSize: '34px' }
                                        })
                                    )
                                )
                            ),
                            React.createElement('p', {
                                className: `text-center font-medium flex items-center justify-center gap-2 ${
                                    currentSlideData.leftImage.label.includes('✅') || currentSlideData.leftImage.label.includes('Perfect') || currentSlideData.leftImage.label.includes('Best')
                                        ? 'text-green-200'
                                        : 'text-red-200'
                                }`
                            }, currentSlideData.leftImage.label)
                        )
                    ),
                    
                    // Right Image (Dynamic styling based on content)
                    React.createElement('div', {
                        className: 'comparison-card group'
                    },
                        React.createElement('div', {
                            className: `relative overflow-hidden rounded-2xl p-5 transition-all duration-300 group-hover:scale-[1.02] group-hover:shadow-2xl ${
                                currentSlideData.rightImage.label.includes('✅') || currentSlideData.rightImage.label.includes('Perfect') || currentSlideData.rightImage.label.includes('Best')
                                    ? 'bg-gradient-to-br from-green-500/20 to-green-600/20 border border-green-500/30'
                                    : 'bg-gradient-to-br from-red-500/20 to-red-600/20 border border-red-500/30'
                            }`,
                            style: {
                                backdropFilter: 'blur(10px)',
                                WebkitBackdropFilter: 'blur(10px)'
                            }
                        },
                            React.createElement('div', {
                                className: 'rounded-xl overflow-hidden bg-gray-800/50 mb-3 relative',
                                style: { width: '337px', height: '505px' }
                            },
                                React.createElement('img', {
                                    src: currentSlideData.rightImage.src,
                                    alt: currentSlideData.rightImage.alt,
                                    className: 'w-full h-full object-cover transition-transform duration-300 group-hover:scale-105',
                                    loading: 'lazy'
                                }),
                                React.createElement('div', {
                                    className: `absolute inset-0 transition-colors duration-300 ${
                                        currentSlideData.rightImage.label.includes('✅') || currentSlideData.rightImage.label.includes('Perfect') || currentSlideData.rightImage.label.includes('Best')
                                            ? 'bg-green-500/10 group-hover:bg-green-500/5'
                                            : 'bg-red-500/10 group-hover:bg-red-500/5'
                                    }`
                                }),
                                // Contextual Status Icon Overlay
                                React.createElement('div', {
                                    className: 'absolute top-3 right-3 z-10',
                                    style: {
                                        backdropFilter: 'blur(0)',
                                        WebkitBackdropFilter: 'blur(0)'
                                    }
                                },
                                    React.createElement('div', {
                                        className: `status-icon-overlay rounded-full p-1.5 ${
                                            currentSlideData.rightImage.label.includes('✅') || currentSlideData.rightImage.label.includes('Perfect') || currentSlideData.rightImage.label.includes('Best')
                                                ? 'success'
                                                : 'error'
                                        }`,
                                        'aria-label': currentSlideData.rightImage.label.includes('✅') || currentSlideData.rightImage.label.includes('Perfect') || currentSlideData.rightImage.label.includes('Best')
                                            ? 'Correct example'
                                            : 'Incorrect example'
                                    },
                                        React.createElement('span', {
                                            className: `iconify transition-all duration-300 ${
                                                currentSlideData.rightImage.label.includes('✅') || currentSlideData.rightImage.label.includes('Perfect') || currentSlideData.rightImage.label.includes('Best')
                                                    ? 'text-green-600'
                                                    : 'text-red-600'
                                            }`,
                                            'data-icon': currentSlideData.rightImage.label.includes('✅') || currentSlideData.rightImage.label.includes('Perfect') || currentSlideData.rightImage.label.includes('Best')
                                                ? 'solar:check-circle-bold-duotone'
                                                : 'solar:close-square-bold-duotone',
                                            style: { fontSize: '34px' }
                                        })
                                    )
                                )
                            ),
                            React.createElement('p', {
                                className: `text-center font-medium flex items-center justify-center gap-2 ${
                                    currentSlideData.rightImage.label.includes('✅') || currentSlideData.rightImage.label.includes('Perfect') || currentSlideData.rightImage.label.includes('Best')
                                        ? 'text-green-200'
                                        : 'text-red-200'
                                }`
                            }, currentSlideData.rightImage.label)
                        )
                    )
                )
            ),
            
            // Navigation Footer - Fixed at bottom
            React.createElement('div', {
                className: 'flex items-center justify-between p-6 border-t border-white/10 flex-shrink-0'
            },
                // Previous Button
                React.createElement('button', {
                    onClick: prevSlide,
                    className: 'flex items-center gap-2 px-4 py-2 rounded-xl bg-white/5 hover:bg-white/10 text-gray-300 hover:text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400/50 disabled:opacity-50',
                    'aria-label': 'Previous slide'
                },
                    React.createElement('span', {
                        className: 'iconify',
                        'data-icon': 'solar:arrow-left-linear',
                        style: { fontSize: '16px' }
                    }),
                    'Previous'
                ),
                
                // Bullet Navigation
                React.createElement('div', {
                    className: 'flex items-center gap-3'
                },
                    slides.map((slide, index) => 
                        React.createElement('button', {
                            key: slide.id,
                            onClick: () => goToSlide(index),
                            className: `w-3 h-3 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400/50 ${
                                index === currentSlide 
                                    ? 'bg-blue-400 scale-125 shadow-lg shadow-blue-400/50' 
                                    : 'bg-white/30 hover:bg-white/50 hover:scale-110'
                            }`,
                            'aria-label': `Go to slide ${index + 1}: ${slide.title}`,
                            'aria-current': index === currentSlide ? 'true' : 'false'
                        })
                    )
                ),
                
                // Next Button
                React.createElement('button', {
                    onClick: nextSlide,
                    className: 'flex items-center gap-2 px-4 py-2 rounded-xl bg-white/5 hover:bg-white/10 text-gray-300 hover:text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400/50',
                    'aria-label': 'Next slide'
                },
                    'Next',
                    React.createElement('span', {
                        className: 'iconify',
                        'data-icon': 'solar:arrow-right-linear',
                        style: { fontSize: '16px' }
                    })
                )
            ),
            
            // Slide Indicator
            React.createElement('div', {
                className: 'absolute top-6 right-20 px-3 py-1 rounded-full bg-white/10 text-white/70 text-sm font-medium',
                style: {
                    backdropFilter: 'blur(10px)',
                    WebkitBackdropFilter: 'blur(10px)'
                }
            }, `${currentSlide + 1} of ${slides.length}`)
        )
    );
}; 